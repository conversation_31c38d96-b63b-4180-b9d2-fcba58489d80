import React, { useState, useRef, KeyboardEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Paperclip, Smile } from 'lucide-react';
import { cn } from '@/lib/utils';
import {MessageAttachment} from "@/services";

interface ChatInputProps {
  onSendMessage: (content: string, attachments?: MessageAttachment[]) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "Nhập tin nhắn...",
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      onSendMessage(trimmedMessage);
      setMessage('');
      setIsTyping(false);
      
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (value: string) => {
    setMessage(value);
    setIsTyping(value.length > 0);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };

  const handleAttachment = () => {
    // TODO: Implement file attachment
    console.log('Attachment clicked');
  };

  const handleEmoji = () => {
    // TODO: Implement emoji picker
    console.log('Emoji clicked');
  };

  return (
    <div className="border-t border-border bg-card p-4">
      <div className="flex items-end gap-2">
        {/* Attachment button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={handleAttachment}
          disabled={disabled}
          className="shrink-0"
          title="Đính kèm file"
        >
          <Paperclip className="h-4 w-4" />
        </Button>

        {/* Message input */}
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "min-h-[40px] max-h-[120px] resize-none pr-10",
              "focus:ring-1 focus:ring-primary"
            )}
            rows={1}
          />
          
          {/* Emoji button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={handleEmoji}
            disabled={disabled}
            className="absolute right-1 top-1 h-8 w-8"
            title="Chọn emoji"
          >
            <Smile className="h-4 w-4" />
          </Button>
        </div>

        {/* Send button */}
        <Button
          onClick={handleSend}
          disabled={disabled || !message.trim()}
          size="icon"
          className="shrink-0"
          title="Gửi tin nhắn"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>

      {/* Typing indicator */}
      {isTyping && (
        <div className="mt-2 text-xs text-muted-foreground">
          Nhấn Enter để gửi, Shift + Enter để xuống dòng
        </div>
      )}
    </div>
  );
};
