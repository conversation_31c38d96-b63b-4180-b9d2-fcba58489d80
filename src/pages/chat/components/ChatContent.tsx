import React, { useEffect, useRef } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ChatMessage, Conversation } from '@/services/ChatService';

interface ChatContentProps {
  messages: ChatMessage[];
  currentUserId: string;
  conversation: Conversation | null;
  loading?: boolean;
}

export const ChatContent: React.FC<ChatContentProps> = ({
  messages,
  currentUserId,
  conversation,
  loading = false,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Hôm nay';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hôm qua';
    } else {
      return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    }
  };

  const getInitials = (name: string | undefined | null) => {
    if (!name || typeof name !== 'string') {
      return 'KH'; // Khách hàng
    }
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const groupMessagesByDate = (messages: ChatMessage[]) => {
    const groups: { [key: string]: ChatMessage[] } = {};

    messages.forEach(message => {
      const dateKey = new Date(message.sentAt).toDateString();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });

    return Object.entries(groups).map(([dateKey, messages]) => ({
      date: dateKey,
      messages
    }));
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-muted-foreground">Đang tải tin nhắn...</p>
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
          <h3 className="font-medium mb-2">Chưa có tin nhắn nào</h3>
          <p className="text-muted-foreground text-sm">
            Bắt đầu cuộc trò chuyện bằng cách gửi tin nhắn đầu tiên
          </p>
        </div>
      </div>
    );
  }

  const messageGroups = groupMessagesByDate(messages);

  return (
    <ScrollArea className="flex-1" ref={scrollAreaRef}>
      <div className="p-4 space-y-4">
        {messageGroups.map(({ date, messages }) => (
          <div key={date}>
            {/* Date separator */}
            <div className="flex items-center justify-center my-4">
              <Badge variant="outline" className="bg-background">
                <Clock className="h-3 w-3 mr-1" />
                {formatDate(date)}
              </Badge>
            </div>

            {/* Messages for this date */}
            <div className="space-y-3">
              {messages.map((message) => {
                const senderId = typeof message.senderId === 'string' ? message.senderId : message.senderId._id;
                const isCurrentUser = senderId === currentUserId;
                const isSupport = message.senderRole === 'support';

                return (
                  <div
                    key={message._id}
                    className={cn(
                      "flex gap-3",
                      isCurrentUser ? "justify-end" : "justify-start"
                    )}
                  >
                    {!isCurrentUser && (
                      <Avatar className="h-8 w-8 mt-1">
                        <AvatarImage
                          src={conversation?.customerId?.avatarId ? `/api/files/content/${conversation.customerId.avatarId}` : undefined}
                          alt={conversation?.customerId?.fullName}
                        />
                        <AvatarFallback className="bg-muted text-muted-foreground text-xs">
                          {getInitials(conversation?.customerId?.fullName)}
                        </AvatarFallback>
                      </Avatar>
                    )}

                    <div
                      className={cn(
                        "max-w-[70%] rounded-lg px-3 py-2",
                        isCurrentUser
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted",
                        message.isTemporary && "opacity-60" // Dim temporary messages
                      )}
                    >
                      <p className="text-sm whitespace-pre-wrap break-words">
                        {message.content || message.text}
                      </p>

                      {/* Show attachments if any */}
                      {message.attachments && message.attachments.length > 0 && (
                        <div className="mt-2 space-y-1">
                          {message.attachments.map((attachment, index) => (
                            <div key={index} className="flex items-center gap-2 p-2 bg-background/10 rounded">
                              <span className="text-xs">{attachment.fileName}</span>
                            </div>
                          ))}
                        </div>
                      )}

                      <div className={cn(
                        "flex items-center gap-1 mt-1",
                        isCurrentUser ? "justify-end" : "justify-start"
                      )}>
                        <span className={cn(
                          "text-xs",
                          isCurrentUser
                            ? "text-primary-foreground/70"
                            : "text-muted-foreground"
                        )}>
                          {formatTime(message.sentAt)}
                        </span>

                        {message.isTemporary && isCurrentUser && (
                          <Badge variant="outline" className="text-xs h-4 px-1">
                            Đang gửi...
                          </Badge>
                        )}

                        {message.read && isCurrentUser && !message.isTemporary && (
                          <Badge variant="secondary" className="text-xs h-4 px-1">
                            Đã đọc
                          </Badge>
                        )}
                      </div>
                    </div>

                    {isCurrentUser && (
                      <Avatar className="h-8 w-8 mt-1">
                        <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                          {isSupport ? 'SP' : 'ME'}
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        ))}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>
    </ScrollArea>
  );
};
