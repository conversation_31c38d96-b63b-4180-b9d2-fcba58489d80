import { DefaultLayout } from "@/components/layout/DefaultLayout";
import { SEO } from "@/components/SEO";
import { ChatPage } from "./ChatPage";

export default function Chat() {
  return (
    <DefaultLayout>
      <SEO title="Chat với khách hàng" description="Tương tác với khách hàng qua hệ thống chat thời gian thực" />
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Chat với khách hàng</h1>
          <p className="text-muted-foreground">
            Tương tác với khách hàng qua hệ thống chat thời gian thực
          </p>
        </div>

        <ChatPage />
      </div>
    </DefaultLayout>
  );
}

// Export các component chính
export { ChatPage } from './ChatPage';
export { ChatSidebar } from './components/ChatSidebar';
export { ChatHeader } from './components/ChatHeader';
export { ChatContent } from './components/ChatContent';
export { ChatInput } from './components/ChatInput';
