import { useState, useEffect } from "react";
import { ShopLayout } from "@/components/layout/ShopLayout";
import { SEO } from "@/components/SEO";
import { PageHeader } from "@/components/common/PageHeader";
import { SearchBar } from "@/components/common/SearchBar";
import { DataTable, Column } from "@/components/common/DataTable";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Package, 
  Eye, 
  MessageSquare,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle,
  Filter
} from "lucide-react";

interface Order {
  id: string;
  orderCode: string;
  customerName: string;
  customerPhone: string;
  products: string;
  totalAmount: number;
  codAmount: number;
  status: 'pending' | 'confirmed' | 'shipping' | 'delivered' | 'cancelled' | 'returned';
  createdAt: string;
  deliveryDate?: string;
  trackingCode?: string;
}

export default function ShopOrders() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setOrders([
          {
            id: "1",
            orderCode: "DH001234",
            customerName: "Nguyễn Văn A",
            customerPhone: "0901234567",
            products: "Áo thun nam, Quần jean",
            totalAmount: 450000,
            codAmount: 450000,
            status: "delivered",
            createdAt: "2024-01-15 09:30",
            deliveryDate: "2024-01-17 14:30",
            trackingCode: "GHN123456789"
          },
          {
            id: "2",
            orderCode: "DH001235",
            customerName: "Trần Thị B",
            customerPhone: "0912345678",
            products: "Váy đầm nữ",
            totalAmount: 320000,
            codAmount: 320000,
            status: "shipping",
            createdAt: "2024-01-14 16:20",
            trackingCode: "GHN987654321"
          },
          {
            id: "3",
            orderCode: "DH001236",
            customerName: "Lê Văn C",
            customerPhone: "0923456789",
            products: "Giày thể thao, Tất nam",
            totalAmount: 680000,
            codAmount: 680000,
            status: "confirmed",
            createdAt: "2024-01-13 11:15"
          },
          {
            id: "4",
            orderCode: "DH001237",
            customerName: "Phạm Thị D",
            customerPhone: "0934567890",
            products: "Túi xách nữ",
            totalAmount: 250000,
            codAmount: 250000,
            status: "pending",
            createdAt: "2024-01-12 08:45"
          },
          {
            id: "5",
            orderCode: "DH001238",
            customerName: "Hoàng Văn E",
            customerPhone: "0945678901",
            products: "Áo khoác nam",
            totalAmount: 420000,
            codAmount: 0,
            status: "cancelled",
            createdAt: "2024-01-11 14:30"
          }
        ]);
      } catch (error) {
        console.error("Error fetching orders:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Chờ xử lý</Badge>;
      case 'confirmed':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Đã xác nhận</Badge>;
      case 'shipping':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">Đang giao</Badge>;
      case 'delivered':
        return <Badge variant="default" className="bg-green-100 text-green-800">Đã giao</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Đã hủy</Badge>;
      case 'returned':
        return <Badge variant="default" className="bg-orange-100 text-orange-800">Hoàn trả</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'confirmed':
        return <CheckCircle className="h-4 w-4" />;
      case 'shipping':
        return <Truck className="h-4 w-4" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <AlertCircle className="h-4 w-4" />;
      case 'returned':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderCode.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customerPhone.includes(searchQuery);
    
    if (activeTab === "all") return matchesSearch;
    return matchesSearch && order.status === activeTab;
  });

  const columns: Column<Order>[] = [
    {
      header: "Mã đơn hàng",
      cell: (order) => (
        <div className="font-medium">#{order.orderCode}</div>
      ),
      className: "w-[120px]"
    },
    {
      header: "Khách hàng",
      cell: (order) => (
        <div>
          <div className="font-medium">{order.customerName}</div>
          <div className="text-sm text-muted-foreground">{order.customerPhone}</div>
        </div>
      )
    },
    {
      header: "Sản phẩm",
      cell: (order) => (
        <div className="max-w-[200px] truncate" title={order.products}>
          {order.products}
        </div>
      )
    },
    {
      header: "Tổng tiền",
      cell: (order) => (
        <div>
          <div className="font-medium">{formatCurrency(order.totalAmount)}</div>
          {order.codAmount > 0 && (
            <div className="text-sm text-muted-foreground">
              COD: {formatCurrency(order.codAmount)}
            </div>
          )}
        </div>
      ),
      className: "w-[120px]"
    },
    {
      header: "Trạng thái",
      cell: (order) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(order.status)}
          {getStatusBadge(order.status)}
        </div>
      ),
      className: "w-[140px]"
    },
    {
      header: "Ngày tạo",
      cell: (order) => (
        <div className="text-sm">
          {order.createdAt}
          {order.deliveryDate && (
            <div className="text-muted-foreground">
              Giao: {order.deliveryDate}
            </div>
          )}
        </div>
      ),
      className: "w-[150px]"
    },
    {
      header: "Hành động",
      cell: (order) => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" title="Xem chi tiết">
            <Eye className="h-4 w-4" />
          </Button>
          {(order.status === 'pending' || order.status === 'confirmed') && (
            <Button variant="ghost" size="sm" title="Liên hệ hỗ trợ">
              <MessageSquare className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
      className: "w-[100px]"
    }
  ];

  const getTabCount = (status: string) => {
    if (status === "all") return orders.length;
    return orders.filter(order => order.status === status).length;
  };

  return (
    <ShopLayout>
      <SEO title="Đơn hàng của tôi - Shop" description="Quản lý và theo dõi đơn hàng" />
      <div className="space-y-6">
        <PageHeader
          title="Đơn hàng của tôi"
          description="Theo dõi và quản lý các đơn hàng của bạn"
          actions={
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Lọc
            </Button>
          }
        />

        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Danh sách đơn hàng
              </CardTitle>
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="Tìm kiếm đơn hàng, khách hàng..."
                className="w-full sm:w-80"
              />
            </div>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList className="grid w-full grid-cols-7">
                <TabsTrigger value="all">
                  Tất cả ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger value="pending">
                  Chờ xử lý ({getTabCount("pending")})
                </TabsTrigger>
                <TabsTrigger value="confirmed">
                  Đã xác nhận ({getTabCount("confirmed")})
                </TabsTrigger>
                <TabsTrigger value="shipping">
                  Đang giao ({getTabCount("shipping")})
                </TabsTrigger>
                <TabsTrigger value="delivered">
                  Đã giao ({getTabCount("delivered")})
                </TabsTrigger>
                <TabsTrigger value="cancelled">
                  Đã hủy ({getTabCount("cancelled")})
                </TabsTrigger>
                <TabsTrigger value="returned">
                  Hoàn trả ({getTabCount("returned")})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab}>
                <DataTable
                  data={filteredOrders}
                  columns={columns}
                  loading={loading}
                  emptyMessage="Không tìm thấy đơn hàng nào"
                  showPagination={false}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </ShopLayout>
  );
}
