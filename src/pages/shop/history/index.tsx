import { useState, useEffect } from "react";
import { ShopLayout } from "@/components/layout/ShopLayout";
import { SEO } from "@/components/SEO";
import { PageHeader } from "@/components/common/PageHeader";
import { SearchBar } from "@/components/common/SearchBar";
import { DataTable, Column } from "@/components/common/DataTable";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  MessageSquare, 
  PhoneCall, 
  Eye, 
  Star,
  Calendar,
  Filter
} from "lucide-react";

interface SupportHistory {
  id: string;
  type: 'chat' | 'call';
  title: string;
  description: string;
  status: 'completed' | 'in-progress' | 'pending';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  updatedAt: string;
  agent?: string;
  rating?: number;
  duration?: string;
}

export default function ShopHistory() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [history, setHistory] = useState<SupportHistory[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setHistory([
          {
            id: "1",
            type: "chat",
            title: "Hỗ trợ đơn hàng #DH001234",
            description: "Khiếu nại về chất lượng sản phẩm",
            status: "completed",
            priority: "high",
            createdAt: "2024-01-15 09:30",
            updatedAt: "2024-01-15 14:45",
            agent: "Nguyễn Văn A",
            rating: 5
          },
          {
            id: "2",
            type: "call",
            title: "Cập nhật COD đơn #DH001230",
            description: "Thay đổi thông tin thu hộ",
            status: "completed",
            priority: "medium",
            createdAt: "2024-01-14 16:20",
            updatedAt: "2024-01-14 16:35",
            agent: "Trần Thị B",
            rating: 4,
            duration: "5:32"
          },
          {
            id: "3",
            type: "chat",
            title: "Đơn bị giữ tại bưu cục",
            description: "Đơn hàng #DH001228 bị giữ lại",
            status: "in-progress",
            priority: "medium",
            createdAt: "2024-01-13 11:15",
            updatedAt: "2024-01-13 15:20",
            agent: "Lê Văn C"
          },
          {
            id: "4",
            type: "call",
            title: "Hướng dẫn sử dụng hệ thống",
            description: "Hỗ trợ sử dụng tính năng quản lý đơn hàng",
            status: "completed",
            priority: "low",
            createdAt: "2024-01-12 08:45",
            updatedAt: "2024-01-12 09:30",
            agent: "Phạm Thị D",
            rating: 5,
            duration: "8:45"
          },
          {
            id: "5",
            type: "chat",
            title: "Thắc mắc về phí vận chuyển",
            description: "Hỏi về cách tính phí ship",
            status: "pending",
            priority: "low",
            createdAt: "2024-01-11 14:30",
            updatedAt: "2024-01-11 14:30"
          }
        ]);
      } catch (error) {
        console.error("Error fetching history:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Hoàn thành</Badge>;
      case 'in-progress':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Đang xử lý</Badge>;
      case 'pending':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Chờ xử lý</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">Cao</Badge>;
      case 'medium':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Trung bình</Badge>;
      case 'low':
        return <Badge variant="secondary">Thấp</Badge>;
      default:
        return <Badge variant="secondary">{priority}</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    return type === 'chat' ? <MessageSquare className="h-4 w-4" /> : <PhoneCall className="h-4 w-4" />;
  };

  const renderRating = (rating?: number) => {
    if (!rating) return <span className="text-muted-foreground">Chưa đánh giá</span>;
    
    return (
      <div className="flex items-center gap-1">
        {Array.from({ length: 5 }, (_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm">({rating}/5)</span>
      </div>
    );
  };

  const filteredHistory = history.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (activeTab === "all") return matchesSearch;
    if (activeTab === "chat") return matchesSearch && item.type === "chat";
    if (activeTab === "call") return matchesSearch && item.type === "call";
    if (activeTab === "completed") return matchesSearch && item.status === "completed";
    if (activeTab === "pending") return matchesSearch && item.status !== "completed";
    
    return matchesSearch;
  });

  const columns: Column<SupportHistory>[] = [
    {
      header: "Loại",
      cell: (item) => (
        <div className="flex items-center gap-2">
          {getTypeIcon(item.type)}
          <span className="capitalize">{item.type === 'chat' ? 'Chat' : 'Cuộc gọi'}</span>
        </div>
      ),
      className: "w-[100px]"
    },
    {
      header: "Tiêu đề",
      cell: (item) => (
        <div>
          <div className="font-medium">{item.title}</div>
          <div className="text-sm text-muted-foreground">{item.description}</div>
        </div>
      )
    },
    {
      header: "Trạng thái",
      cell: (item) => getStatusBadge(item.status),
      className: "w-[120px]"
    },
    {
      header: "Ưu tiên",
      cell: (item) => getPriorityBadge(item.priority),
      className: "w-[100px]"
    },
    {
      header: "Nhân viên",
      cell: (item) => item.agent || <span className="text-muted-foreground">Chưa phân công</span>,
      className: "w-[150px]"
    },
    {
      header: "Thời gian",
      cell: (item) => (
        <div className="text-sm">
          <div>Tạo: {item.createdAt}</div>
          <div className="text-muted-foreground">Cập nhật: {item.updatedAt}</div>
        </div>
      ),
      className: "w-[180px]"
    },
    {
      header: "Đánh giá",
      cell: (item) => renderRating(item.rating),
      className: "w-[150px]"
    },
    {
      header: "Hành động",
      cell: (item) => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
          {item.status === 'completed' && !item.rating && (
            <Button variant="ghost" size="sm">
              <Star className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
      className: "w-[100px]"
    }
  ];

  const getTabCount = (tab: string) => {
    switch (tab) {
      case "all":
        return history.length;
      case "chat":
        return history.filter(item => item.type === "chat").length;
      case "call":
        return history.filter(item => item.type === "call").length;
      case "completed":
        return history.filter(item => item.status === "completed").length;
      case "pending":
        return history.filter(item => item.status !== "completed").length;
      default:
        return 0;
    }
  };

  return (
    <ShopLayout>
      <SEO title="Lịch sử hỗ trợ - Shop" description="Xem lại các yêu cầu hỗ trợ đã thực hiện" />
      <div className="space-y-6">
        <PageHeader
          title="Lịch sử hỗ trợ"
          description="Xem lại các yêu cầu hỗ trợ và đánh giá dịch vụ"
          actions={
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Lọc
            </Button>
          }
        />

        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Danh sách yêu cầu hỗ trợ
              </CardTitle>
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="Tìm kiếm yêu cầu hỗ trợ..."
                className="w-full sm:w-80"
              />
            </div>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="all">
                  Tất cả ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger value="chat">
                  Chat ({getTabCount("chat")})
                </TabsTrigger>
                <TabsTrigger value="call">
                  Cuộc gọi ({getTabCount("call")})
                </TabsTrigger>
                <TabsTrigger value="completed">
                  Hoàn thành ({getTabCount("completed")})
                </TabsTrigger>
                <TabsTrigger value="pending">
                  Đang xử lý ({getTabCount("pending")})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab}>
                <DataTable
                  data={filteredHistory}
                  columns={columns}
                  loading={loading}
                  emptyMessage="Không tìm thấy yêu cầu hỗ trợ nào"
                  showPagination={false}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </ShopLayout>
  );
}
