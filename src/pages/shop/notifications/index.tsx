import { useState, useEffect } from "react";
import { ShopLayout } from "@/components/layout/ShopLayout";
import { SEO } from "@/components/SEO";
import { PageHeader } from "@/components/common/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Bell, 
  Package, 
  MessageSquare, 
  PhoneCall,
  CheckCircle,
  Trash2,
  MarkAsUnread,
  Settings
} from "lucide-react";

interface Notification {
  id: string;
  type: 'order' | 'support' | 'system' | 'promotion';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
  priority: 'low' | 'medium' | 'high';
}

export default function ShopNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeTab, setActiveTab] = useState("all");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setNotifications([
          {
            id: "1",
            type: "order",
            title: "Đơn hàng #DH001234 đã được giao",
            message: "Đơn hàng của bạn đã được giao thành công đến khách hàng Nguyễn Văn A",
            isRead: false,
            createdAt: "2024-01-15 14:30",
            actionUrl: "/shop/orders/DH001234",
            priority: "medium"
          },
          {
            id: "2",
            type: "support",
            title: "CSKH đã phản hồi yêu cầu hỗ trợ",
            message: "Nhân viên Trần Thị B đã phản hồi yêu cầu hỗ trợ về COD đơn hàng #DH001230",
            isRead: false,
            createdAt: "2024-01-15 11:20",
            actionUrl: "/shop/support/conversation/2",
            priority: "high"
          },
          {
            id: "3",
            type: "order",
            title: "Đơn hàng #DH001235 đang được vận chuyển",
            message: "Đơn hàng của bạn đã được bàn giao cho đơn vị vận chuyển",
            isRead: true,
            createdAt: "2024-01-14 16:45",
            actionUrl: "/shop/orders/DH001235",
            priority: "medium"
          },
          {
            id: "4",
            type: "system",
            title: "Cập nhật hệ thống",
            message: "Hệ thống sẽ bảo trì từ 2:00 - 4:00 sáng ngày 16/01/2024",
            isRead: true,
            createdAt: "2024-01-13 18:00",
            priority: "low"
          },
          {
            id: "5",
            type: "promotion",
            title: "Chương trình khuyến mãi mới",
            message: "Giảm 20% phí vận chuyển cho tất cả đơn hàng trong tháng 1",
            isRead: false,
            createdAt: "2024-01-12 09:00",
            priority: "medium"
          },
          {
            id: "6",
            type: "order",
            title: "Đơn hàng #DH001236 cần xác nhận",
            message: "Khách hàng Lê Văn C đã đặt đơn hàng mới, vui lòng xác nhận",
            isRead: true,
            createdAt: "2024-01-11 15:30",
            actionUrl: "/shop/orders/DH001236",
            priority: "high"
          }
        ]);
      } catch (error) {
        console.error("Error fetching notifications:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <Package className="h-5 w-5 text-blue-600" />;
      case 'support':
        return <MessageSquare className="h-5 w-5 text-green-600" />;
      case 'system':
        return <Settings className="h-5 w-5 text-gray-600" />;
      case 'promotion':
        return <Bell className="h-5 w-5 text-purple-600" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'order':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Đơn hàng</Badge>;
      case 'support':
        return <Badge variant="default" className="bg-green-100 text-green-800">Hỗ trợ</Badge>;
      case 'system':
        return <Badge variant="secondary">Hệ thống</Badge>;
      case 'promotion':
        return <Badge variant="default" className="bg-purple-100 text-purple-800">Khuyến mãi</Badge>;
      default:
        return <Badge variant="secondary">{type}</Badge>;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500';
      case 'medium':
        return 'border-l-yellow-500';
      case 'low':
        return 'border-l-gray-300';
      default:
        return 'border-l-gray-300';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === "all") return true;
    if (activeTab === "unread") return !notification.isRead;
    if (activeTab === "read") return notification.isRead;
    return notification.type === activeTab;
  });

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const markAsUnread = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: false }
          : notification
      )
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  };

  const getTabCount = (tab: string) => {
    switch (tab) {
      case "all":
        return notifications.length;
      case "unread":
        return notifications.filter(n => !n.isRead).length;
      case "read":
        return notifications.filter(n => n.isRead).length;
      case "order":
        return notifications.filter(n => n.type === "order").length;
      case "support":
        return notifications.filter(n => n.type === "support").length;
      case "system":
        return notifications.filter(n => n.type === "system").length;
      case "promotion":
        return notifications.filter(n => n.type === "promotion").length;
      default:
        return 0;
    }
  };

  return (
    <ShopLayout>
      <SEO title="Thông báo - Shop" description="Xem các thông báo và cập nhật mới nhất" />
      <div className="space-y-6">
        <PageHeader
          title="Thông báo"
          description="Theo dõi các thông báo và cập nhật quan trọng"
          actions={
            <Button onClick={markAllAsRead} variant="outline">
              <CheckCircle className="h-4 w-4 mr-2" />
              Đánh dấu tất cả đã đọc
            </Button>
          }
        />

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Danh sách thông báo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList className="grid w-full grid-cols-7">
                <TabsTrigger value="all">
                  Tất cả ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger value="unread">
                  Chưa đọc ({getTabCount("unread")})
                </TabsTrigger>
                <TabsTrigger value="read">
                  Đã đọc ({getTabCount("read")})
                </TabsTrigger>
                <TabsTrigger value="order">
                  Đơn hàng ({getTabCount("order")})
                </TabsTrigger>
                <TabsTrigger value="support">
                  Hỗ trợ ({getTabCount("support")})
                </TabsTrigger>
                <TabsTrigger value="system">
                  Hệ thống ({getTabCount("system")})
                </TabsTrigger>
                <TabsTrigger value="promotion">
                  Khuyến mãi ({getTabCount("promotion")})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab}>
                {loading ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Đang tải thông báo...
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Không có thông báo nào
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredNotifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 border rounded-lg border-l-4 ${getPriorityColor(notification.priority)} ${
                          !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-950/20' : 'bg-background'
                        } hover:bg-muted/50 transition-colors`}
                      >
                        <div className="flex items-start gap-4">
                          <div className="flex-shrink-0 mt-1">
                            {getTypeIcon(notification.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-4">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className={`font-medium ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}>
                                    {notification.title}
                                  </h4>
                                  {!notification.isRead && (
                                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground mb-2">
                                  {notification.message}
                                </p>
                                <div className="flex items-center gap-2">
                                  {getTypeBadge(notification.type)}
                                  <span className="text-xs text-muted-foreground">
                                    {notification.createdAt}
                                  </span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                {notification.isRead ? (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => markAsUnread(notification.id)}
                                    title="Đánh dấu chưa đọc"
                                  >
                                    <MarkAsUnread className="h-4 w-4" />
                                  </Button>
                                ) : (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => markAsRead(notification.id)}
                                    title="Đánh dấu đã đọc"
                                  >
                                    <CheckCircle className="h-4 w-4" />
                                  </Button>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => deleteNotification(notification.id)}
                                  title="Xóa thông báo"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </ShopLayout>
  );
}
