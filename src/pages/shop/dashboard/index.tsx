import { useState, useEffect } from "react";
import { ShopLayout } from "@/components/layout/ShopLayout";
import { Package, MessageSquare, PhoneCall, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { SEO } from "@/components/SEO";
import { useNavigate } from "react-router-dom";

// Import common components
import { PageHeader } from "@/components/common/PageHeader";
import { InfoCard } from "@/components/common/InfoCard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface ShopDashboardMetric {
  id: string;
  title: string;
  value: number | string;
  description: string;
  icon: React.ReactNode;
  colorClass: string;
  onClick?: () => void;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  colorClass: string;
  onClick: () => void;
}

interface RecentActivity {
  id: string;
  type: 'order' | 'support' | 'call';
  title: string;
  description: string;
  time: string;
  status: 'pending' | 'completed' | 'in-progress';
}

export default function ShopDashboard() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<ShopDashboardMetric[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);

  const quickActions: QuickAction[] = [
    {
      id: "chat-support",
      title: "Trò chuyện CSKH",
      description: "Nhận hỗ trợ nhanh qua chat",
      icon: <MessageSquare className="h-6 w-6" />,
      colorClass: "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",
      onClick: () => navigate("/shop/support")
    },
    {
      id: "call-support",
      title: "Gọi tổng đài",
      description: "Liên hệ trực tiếp qua điện thoại",
      icon: <PhoneCall className="h-6 w-6" />,
      colorClass: "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400",
      onClick: () => navigate("/shop/call")
    },
    {
      id: "view-orders",
      title: "Xem đơn hàng",
      description: "Theo dõi trạng thái đơn hàng",
      icon: <Package className="h-6 w-6" />,
      colorClass: "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400",
      onClick: () => navigate("/shop/orders")
    }
  ];

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setMetrics([
          {
            id: "total-orders",
            title: "Tổng đơn hàng",
            value: "156",
            description: "Đơn hàng đã tạo",
            icon: <Package className="h-5 w-5" />,
            colorClass: "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",
            onClick: () => navigate("/shop/orders")
          },
          {
            id: "pending-orders",
            title: "Đơn hàng chờ xử lý",
            value: "8",
            description: "Cần theo dõi",
            icon: <Clock className="h-5 w-5" />,
            colorClass: "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400",
            onClick: () => navigate("/shop/orders?status=pending")
          },
          {
            id: "support-tickets",
            title: "Yêu cầu hỗ trợ",
            value: "3",
            description: "Đang xử lý",
            icon: <MessageSquare className="h-5 w-5" />,
            colorClass: "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400",
            onClick: () => navigate("/shop/history")
          },
          {
            id: "completed-orders",
            title: "Đơn hàng hoàn thành",
            value: "142",
            description: "Đã giao thành công",
            icon: <CheckCircle className="h-5 w-5" />,
            colorClass: "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400",
            onClick: () => navigate("/shop/orders?status=completed")
          }
        ]);

        setRecentActivities([
          {
            id: "1",
            type: "order",
            title: "Đơn hàng #DH001234",
            description: "Đã được giao thành công",
            time: "2 giờ trước",
            status: "completed"
          },
          {
            id: "2",
            type: "support",
            title: "Yêu cầu hỗ trợ COD",
            description: "CSKH đã phản hồi",
            time: "4 giờ trước",
            status: "in-progress"
          },
          {
            id: "3",
            type: "call",
            title: "Cuộc gọi tổng đài",
            description: "Đã hoàn thành cuộc gọi",
            time: "1 ngày trước",
            status: "completed"
          },
          {
            id: "4",
            type: "order",
            title: "Đơn hàng #DH001235",
            description: "Đang chờ xử lý",
            time: "2 ngày trước",
            status: "pending"
          }
        ]);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [navigate]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Hoàn thành</Badge>;
      case 'in-progress':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Đang xử lý</Badge>;
      case 'pending':
        return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Chờ xử lý</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <Package className="h-4 w-4" />;
      case 'support':
        return <MessageSquare className="h-4 w-4" />;
      case 'call':
        return <PhoneCall className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <ShopLayout>
      <SEO title="Trang chính - Shop" description="Trang chính dành cho người bán hàng" />
      <div className="space-y-6">
        <PageHeader
          title="Chào mừng bạn trở lại!"
          description="Theo dõi đơn hàng và nhận hỗ trợ nhanh chóng"
        />

        {/* Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {loading
            ? Array(4)
                .fill(null)
                .map((_, i) => (
                  <InfoCard
                    key={i}
                    title=""
                    value=""
                    loading={true}
                    withBorder={true}
                  />
                ))
            : metrics.map((metric) => (
                <InfoCard
                  key={metric.id}
                  title={metric.title}
                  value={metric.value}
                  icon={metric.icon}
                  description={metric.description}
                  withBorder={true}
                  withHoverEffect={true}
                  onClick={metric.onClick}
                />
              ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Hành động nhanh</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {quickActions.map((action) => (
                <Button
                  key={action.id}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-3 hover:bg-muted/50"
                  onClick={action.onClick}
                >
                  <div className={`rounded-full p-3 ${action.colorClass}`}>
                    {action.icon}
                  </div>
                  <div className="text-center">
                    <div className="font-medium">{action.title}</div>
                    <div className="text-sm text-muted-foreground">{action.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Hoạt động gần đây</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg border">
                  <div className="rounded-full bg-muted p-2">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{activity.title}</div>
                    <div className="text-sm text-muted-foreground">{activity.description}</div>
                  </div>
                  <div className="text-right">
                    {getStatusBadge(activity.status)}
                    <div className="text-xs text-muted-foreground mt-1">{activity.time}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </ShopLayout>
  );
}
