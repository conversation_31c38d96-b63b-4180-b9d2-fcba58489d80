import { useState, useEffect, useCallback } from "react";
import { userService, User } from "@/services/UserService";
import { enhancedToast } from "@/components/common/EnhancedToast";
import { PaginationInfo } from "@/components/common/Pagination";

export function useAdministrators() {
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [administrators, setAdministrators] = useState<User[]>([]);
  const [paginationInfo, setPaginationInfo] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });

  // State cho dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | undefined>(undefined);

  // State cho dialog xác nhận xóa
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | undefined>(undefined);

  // L<PERSON>y danh sách quản trị viên từ API
  const fetchAdministrators = useCallback(async () => {
    try {
      setLoading(true);
      const response = await userService.getAllUsers({
        query: searchQuery,
        page: paginationInfo.page,
        limit: paginationInfo.limit,
        role: "admin" // Lọc theo vai trò admin
      });

      if (response && Array.isArray(response.rows)) {
        setAdministrators(response.rows);
        setPaginationInfo({
          total: response.total || 0,
          page: response.page || 1,
          limit: response.limit || 10,
          totalPages: response.totalPages || 1
        });
      } else {
        console.error("Invalid response format:", response);
        setAdministrators([]);
      }
    } catch (error) {
      console.error("Error fetching administrators:", error);
      enhancedToast.error("Không thể tải danh sách quản trị viên");
      setAdministrators([]);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, paginationInfo.page, paginationInfo.limit]);

  useEffect(() => {
    fetchAdministrators();
  }, [fetchAdministrators]);

  // Xử lý tìm kiếm
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Xử lý phân trang
  const handlePageChange = (page: number) => {
    setPaginationInfo(prev => ({ ...prev, page }));
  };

  // Hàm mở dialog thêm mới
  const handleAddNew = () => {
    setSelectedUser(undefined);
    setDialogOpen(true);
  };

  // Hàm mở dialog chỉnh sửa
  const handleEdit = (user: User) => {
    setSelectedUser(user);
    setDialogOpen(true);
  };

  // Hàm mở dialog xác nhận xóa
  const handleDeleteConfirm = (user: User) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  return {
    administrators,
    loading,
    searchQuery,
    paginationInfo,
    dialogOpen,
    selectedUser,
    deleteDialogOpen,
    userToDelete,
    setDialogOpen,
    setDeleteDialogOpen,
    handleSearch,
    handlePageChange,
    handleAddNew,
    handleEdit,
    handleDeleteConfirm,
    fetchAdministrators
  };
}
