import { Outlet } from "react-router-dom";
import { ThemeSwitcher } from "@/components/theme/ThemeSwitcher";
import { SEO } from "@/components/SEO";

export default function AuthLayout() {
  return (
    <div className="flex min-h-screen">
      <SEO title="<PERSON><PERSON><PERSON> thực" description="<PERSON>ệ thống xác thực GHVN" />

      {/* Left side - Illustration */}
      <div className="hidden lg:flex lg:w-2/3 bg-muted/20 items-center justify-center p-6">
        <img
          src="/assets/images/pages/login-v2.svg"
          alt="Team collaboration illustration"
          className="w-4/5 h-auto"
        />
      </div>

      {/* Right side - Auth form */}
      <div className="w-full lg:w-1/3 flex flex-col items-center justify-center p-6 bg-background">
        <div className="absolute top-4 right-4">
          <ThemeSwitcher />
        </div>

        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <img
              src="/assets/images/logo/logo_ghvn.png"
              alt="GHVN Logo"
              className="w-full mx-auto"
            />
          </div>

          {/* Render nested routes */}
          <Outlet />
        </div>
      </div>
    </div>
  );
}
