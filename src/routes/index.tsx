import { Routes } from "react-router-dom";
import { AuthRoutes } from "./authRoutes";
import { ProtectedRoutes } from "./protectedRoutes";
import { ShopRoutes } from "./shopRoutes";
import { PublicRoutes } from "./publicRoutes";

export const AppRoutes = () => {
  return (
    <Routes>
      {/* Auth Routes */}
      {AuthRoutes}

      {/* Protected Routes */}
      {ProtectedRoutes}

      {/* Shop Routes */}
      {ShopRoutes}

      {/* Public Routes */}
      {PublicRoutes}
    </Routes>
  );
};
