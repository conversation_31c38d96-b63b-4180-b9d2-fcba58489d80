import { Route, Navigate } from "react-router-dom";
import { PrivateRoute } from "@/components/auth/PrivateRoute";

// Dashboard Pages
import Dashboard from "@/pages/dashboard";
import CallCenter from "@/pages/call-center";
import Admins from "@/pages/admins";
import Customers from "@/pages/customers";
import Chat from "@/pages/chat";
import ChatCustomers from "@/pages/chat-customers";
import ProfilePage from "@/pages/profile";

export const ProtectedRoutes = (
  <Route element={<PrivateRoute />}>
    <Route path="/dashboard" element={<Dashboard />} />
    <Route path="/call-center" element={<CallCenter />} />
    <Route path="/admins" element={<Admins />} />
    <Route path="/customers" element={<Customers />} />
    <Route path="/chat" element={<Chat />} />
    <Route path="/chat-customer" element={<ChatCustomers />} />
    {/* Redirect old chat-customer route to new chat route */}
    {/*<Route path="/chat-customer" element={<Navigate to="/chat" replace />} />*/}
    <Route path="/profile" element={<ProfilePage />} />
  </Route>
);
