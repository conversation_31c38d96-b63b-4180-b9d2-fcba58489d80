import { Route } from "react-router-dom";
import { RoleBasedRoute } from "@/components/auth/RoleBasedRoute";

// Shop Pages
import ShopDashboard from "@/pages/shop/dashboard";
import ShopOrders from "@/pages/shop/orders";
import ShopSupport from "@/pages/shop/support";
import ShopCall from "@/pages/shop/call";
import ShopHistory from "@/pages/shop/history";
import ShopNotifications from "@/pages/shop/notifications";
import ProfilePage from "@/pages/profile";

export const ShopRoutes = (
  <Route element={<RoleBasedRoute allowedRoles={["shop"]} redirectTo="/dashboard" />}>
    <Route path="/shop/dashboard" element={<ShopDashboard />} />
    <Route path="/shop/orders" element={<ShopOrders />} />
    <Route path="/shop/support" element={<ShopSupport />} />
    <Route path="/shop/call" element={<ShopCall />} />
    <Route path="/shop/history" element={<ShopHistory />} />
    <Route path="/shop/notifications" element={<ShopNotifications />} />
    <Route path="/shop/profile" element={<ProfilePage />} />
  </Route>
);
