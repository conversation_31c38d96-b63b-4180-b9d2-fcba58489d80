import { useEffect, useRef } from "react";

export function useDebouncedSearch(
  query: string,
  delay: number,
  callback: () => void
) {
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    const handler = setTimeout(() => {
      callback();
    }, delay);

    return () => clearTimeout(handler);
  }, [query, delay, callback]);
}
