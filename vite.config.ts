import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Tải biến môi trường dựa trên mode (development, production, etc.)
  const env = loadEnv(mode, process.cwd(), '');

  // Lấy URL backend từ biến môi trường hoặc sử dụng giá trị mặc định
  const backendUrl = env.VITE_API_BACKEND_URL || 'http://localhost:3000';
  const chatUrl = env.VITE_API_CHAT_URL || 'http://localhost:5055';
  const socketUrl = env.VITE_API_SOCKET_URL || 'ws://localhost:5055';

  // Cấu hình log API dựa trên biến môi trường
  const enableApiLogs = env.VITE_ENABLE_API_LOGS === 'true';

  return {
    server: {
      host: "::",
      port: 8080,
      proxy: {
        // Proxy API requests to backend server
        '/api': {
          target: backendUrl,
          changeOrigin: true,
          secure: false,
          // This is important for cookies to work properly
          cookieDomainRewrite: {
            '*': ''
          },

          configure: (proxy, _options) => {
            if (enableApiLogs) {
              proxy.on('error', (err, _req, _res) => {
                console.log('proxy error', err);
              });
              proxy.on('proxyReq', (proxyReq, req, _res) => {
                console.log('Sending Request to the Target:', req.method, req.url);
              });
              proxy.on('proxyRes', (proxyRes, req, _res) => {
                console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
              });
            }
          },
        },
        '/cskh': {
          target: chatUrl,
          changeOrigin: true,
          secure: false,
          cookieDomainRewrite: {
            '*': ''
          },
        },
        // Proxy upload file requests
        '/upload': {
          target: backendUrl,
          changeOrigin: true,
          secure: false,
          cookieDomainRewrite: {
            '*': ''
          }
        },
        // Proxy socket requests to port 3001 (match backend path)
        '/socket.io': {
          target: socketUrl,
          changeOrigin: true,
          secure: false,
          ws: true, // Enable WebSocket proxying
        }
      }
    },
    plugins: [
      react(),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
  };
});
